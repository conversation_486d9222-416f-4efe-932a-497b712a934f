#!/usr/bin/env python3
"""
Test script for the final fixes:
1. Organization pk field fallback fix
2. Unit detection using plant-level data fix
"""

import sys
import os
sys.path.append('src')

def test_organization_fallback_fix():
    """Test that organization fallback response uses actual UID"""
    print("🔑 Testing Organization Fallback UID Fix...")
    
    try:
        # Simulate the fallback organization response generation
        import hashlib
        import time
        
        def simulate_fallback_organization_response(state: dict, session_id: str):
            """Simulate the fixed fallback organization response"""
            
            def get_research_topic_mock(messages):
                return "Ho-Ping Power Station"
            
            # Fallback organization-level response - CRITICAL FIX: Use actual UID
            fallback_org_uid = state.get("org_uid", "")
            if not fallback_org_uid:
                # Generate emergency UID for fallback
                plant_name = get_research_topic_mock(state.get("messages", []))
                org_hash = hashlib.md5(f"Unknown_{plant_name}".encode()).hexdigest()[:6].upper()
                timestamp = str(int(time.time()))[-8:]
                fallback_org_uid = f"ORG_UN_{org_hash}_{timestamp}"
                state["org_uid"] = fallback_org_uid
                print(f"[Session {session_id}] 🚨 Generated emergency fallback UID: {fallback_org_uid}")
            
            final_content = f"""ORGANIZATION-LEVEL INFORMATION:
{{
  "sk": "org_details",
  "cfpp_type": "Not available",
  "country_name": "Not available",
  "currency_in": "Not available",
  "financial_year": "Not available",
  "organization_name": "Not available",
  "plants_count": 0,
  "plant_types": [],
  "ppa_flag": "Not available",
  "province": "Not available",
  "pk": "{fallback_org_uid}",
  "org_uid": "{fallback_org_uid}",
  "country_flag": null,
  "currency_convert_to": null,
  "currency_listed": [null],
  "off_peak_hours": null,
  "peak_hours": null,
  "plant_addition": null,
  "selected_method": null
}}"""
            
            return final_content, fallback_org_uid
        
        # Test case 1: State with existing UID
        print(f"🔍 Test 1: Fallback with existing UID")
        state_with_uid = {
            "org_uid": "ORG_TW_EXISTING_12345678",
            "messages": []
        }
        
        content, uid = simulate_fallback_organization_response(state_with_uid, "test_session")
        
        print(f"   State UID: {state_with_uid.get('org_uid')}")
        print(f"   Generated content contains: pk: {uid}")
        
        assert f'"pk": "{uid}"' in content, f"Content should contain pk with UID {uid}"
        assert f'"org_uid": "{uid}"' in content, f"Content should contain org_uid with UID {uid}"
        assert '"pk": "default null"' not in content, "Content should not contain 'default null'"
        
        print(f"   ✅ Existing UID preserved in fallback")
        
        # Test case 2: State without UID (emergency generation)
        print(f"\n🔍 Test 2: Fallback with emergency UID generation")
        state_without_uid = {
            "messages": []
        }
        
        content, uid = simulate_fallback_organization_response(state_without_uid, "test_session")
        
        print(f"   Generated UID: {uid}")
        print(f"   Generated content contains: pk: {uid}")
        
        assert uid.startswith("ORG_UN_"), f"Emergency UID should start with ORG_UN_, got: {uid}"
        assert f'"pk": "{uid}"' in content, f"Content should contain pk with UID {uid}"
        assert f'"org_uid": "{uid}"' in content, f"Content should contain org_uid with UID {uid}"
        assert '"pk": "default null"' not in content, "Content should not contain 'default null'"
        
        print(f"   ✅ Emergency UID generated for fallback")
        
        return True
        
    except Exception as e:
        print(f"❌ Organization fallback fix test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_unit_detection_plant_level_fix():
    """Test that unit detection uses plant-level data correctly"""
    print("\n🏭 Testing Unit Detection Plant-Level Fix...")
    
    try:
        # Simulate the fixed unit detection logic
        import json
        
        def simulate_enhanced_unit_detection_fixed(text: str, session_id: str):
            """Simulate the fixed enhanced unit detection"""
            
            # CRITICAL FIX: Extract unit names from PLANT-LEVEL JSON data first (most reliable)
            json_extracted_units = []
            
            # Try to extract from PLANT-LEVEL JSON first (this is the most reliable source)
            try:
                # Look for PLANT-LEVEL INFORMATION section specifically
                plant_start = text.find('PLANT-LEVEL INFORMATION:')
                if plant_start != -1:
                    # Extract from plant level section
                    plant_section = text[plant_start:]
                    start_idx = plant_section.find('{')
                    end_idx = plant_section.rfind('}') + 1
                    if start_idx != -1 and end_idx != -1:
                        json_str = plant_section[start_idx:end_idx]
                        plant_data = json.loads(json_str)
                        
                        # Look for units_id field specifically in plant data
                        if 'units_id' in plant_data and isinstance(plant_data['units_id'], list):
                            json_extracted_units = plant_data['units_id']
                            print(f"[Session {session_id}] 🎯 Found units_id in PLANT JSON: {json_extracted_units}")
                
                # Fallback: Try to extract from any JSON in the text
                if not json_extracted_units:
                    start_idx = text.find('{')
                    end_idx = text.rfind('}') + 1
                    if start_idx != -1 and end_idx != -1:
                        json_str = text[start_idx:end_idx]
                        data = json.loads(json_str)
                        
                        # Look for units_id field in any JSON
                        if 'units_id' in data and isinstance(data['units_id'], list):
                            json_extracted_units = data['units_id']
                            print(f"[Session {session_id}] 🎯 Found units_id in fallback JSON: {json_extracted_units}")
                            
            except Exception as e:
                print(f"[Session {session_id}] ⚠️ JSON extraction failed: {e}")
            
            # If we found units in JSON, use them to create the mapping
            if json_extracted_units:
                # Convert all to strings and create sequential mapping
                original_unit_names = [str(unit) for unit in json_extracted_units]
                
                # Create sequential mapping preserving ALL units (including duplicates)
                sequential_numbers = [str(i+1) for i in range(len(original_unit_names))]
                unit_name_mapping = {i+1: f"Unit {original_unit_names[i]}" for i in range(len(original_unit_names))}
                
                print(f"[Session {session_id}] 🎯 JSON-based unit extraction:")
                print(f"[Session {session_id}] 🎯 Original units: {original_unit_names}")
                print(f"[Session {session_id}] 🎯 Sequential mapping: {sequential_numbers}")
                print(f"[Session {session_id}] 🗺️ Unit name mapping: {unit_name_mapping}")
                
                return sequential_numbers, unit_name_mapping
            
            return [], {}
        
        # Test case 1: Ho-Ping Power Station (2 units)
        ho_ping_text = '''
        ORGANIZATION-LEVEL INFORMATION:
        {
          "sk": "org_details",
          "organization_name": "Ho-Ping Power Company"
        }
        
        PLANT-LEVEL INFORMATION:
        {
          "sk": "plant#coal-fired#1",
          "name": "Ho-Ping Power Station",
          "plant_type": "coal-fired",
          "units_id": ["Unit 1", "Unit 2"],
          "capacity": "1320 MW"
        }
        '''
        
        print(f"🔍 Test 1: Ho-Ping Power Station (should detect 2 units)")
        sequential_numbers, unit_mapping = simulate_enhanced_unit_detection_fixed(ho_ping_text, "test_session")
        
        print(f"   Plant level units_id: ['Unit 1', 'Unit 2']")
        print(f"   Detected sequential: {sequential_numbers}")
        print(f"   Unit mapping: {unit_mapping}")
        
        expected_sequential = ['1', '2']
        expected_mapping = {1: 'Unit Unit 1', 2: 'Unit Unit 2'}
        
        assert sequential_numbers == expected_sequential, f"Expected {expected_sequential}, got {sequential_numbers}"
        assert len(unit_mapping) == 2, f"Should detect 2 units, got {len(unit_mapping)}"
        
        print(f"   ✅ Ho-Ping correctly detected 2 units")
        
        # Test case 2: Taichung Power Station (14 units with duplicates)
        taichung_text = '''
        PLANT-LEVEL INFORMATION:
        {
          "sk": "plant#coal#1",
          "name": "Taichung Power Station",
          "units_id": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1, 2, 3, 4],
          "capacity": "5500 MW"
        }
        '''
        
        print(f"\n🔍 Test 2: Taichung Power Station (should detect 14 units)")
        sequential_numbers, unit_mapping = simulate_enhanced_unit_detection_fixed(taichung_text, "test_session")
        
        print(f"   Plant level units_id: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1, 2, 3, 4]")
        print(f"   Detected sequential: {sequential_numbers}")
        print(f"   Unit mapping count: {len(unit_mapping)}")
        
        expected_sequential = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14']
        
        assert sequential_numbers == expected_sequential, f"Expected {expected_sequential}, got {sequential_numbers}"
        assert len(unit_mapping) == 14, f"Should detect 14 units, got {len(unit_mapping)}"
        
        print(f"   ✅ Taichung correctly detected 14 units")
        
        return True
        
    except Exception as e:
        print(f"❌ Unit detection plant-level fix test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run final fix tests"""
    print("🧪 FINAL FIXES TEST")
    print("=" * 50)
    
    tests = [
        test_organization_fallback_fix,
        test_unit_detection_plant_level_fix
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📊 FINAL FIXES TEST RESULTS:")
    
    passed = sum(results)
    total = len(results)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {test.__name__}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 FINAL FIXES TESTS PASSED!")
        print("\n🚀 Expected results:")
        print("✅ Organization fallback: Uses actual UID (not 'default null')")
        print("✅ Unit detection: Uses plant-level data correctly")
        print("✅ Ho-Ping Power Station: 2 units detected (not 1)")
        print("✅ Taichung Power Station: 14 units detected (not 10)")
        return 0
    else:
        print("⚠️ Some final fixes tests failed.")
        return 1

if __name__ == "__main__":
    exit(main())
