"""
Quick Organization Discovery Module

This module provides lightweight organization discovery functionality
to quickly extract basic organization information and plant lists
without running the full 3-level extraction pipeline.
"""

import os
from typing import Dict, List, Optional
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.runnables import RunnableConfig
from agent.tools_and_schemas import SearchQueryList
from agent.configuration import Configuration
from agent.prompts import get_current_date
from agent.utils import get_research_topic
from agent.state import OverallState
from dotenv import load_dotenv

load_dotenv()

class QuickOrgDiscovery:
    """
    Quick organization discovery for generating UIDs without full extraction
    
    This class provides fast organization discovery to get:
    1. Organization name
    2. Country
    3. Basic plant list for the organization
    
    Optimized for speed (30-60 seconds) vs full extraction (5-10 minutes)
    """
    
    def __init__(self):
        """Initialize quick discovery with optimized settings"""
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash-exp",  # Fast model
            temperature=0,
            max_retries=2,  # Reduced retries for speed
            api_key=os.getenv("GEMINI_API_KEY"),
        )
        self.structured_llm = self.llm.with_structured_output(SearchQueryList)
    
    def generate_quick_queries(self, plant_name: str) -> List[str]:
        """
        Generate 2-3 targeted queries for quick organization discovery
        
        Args:
            plant_name: Name of the power plant
            
        Returns:
            List of optimized search queries
        """
        current_date = get_current_date()
        
        prompt = f"""Generate 2-3 highly targeted search queries to quickly find:
1. The parent organization/company that owns "{plant_name}"
2. The country where "{plant_name}" is located
3. A basic list of other power plants owned by the same organization

Requirements:
- Focus on SPEED and EFFICIENCY
- Use specific, targeted queries
- Avoid broad searches that return too much irrelevant information
- Prioritize official company websites, regulatory filings, and industry databases
- Current date is {current_date}

Format your response as a JSON object with:
- "rationale": Brief explanation of the search strategy
- "query": List of 2-3 targeted search queries

Plant Name: {plant_name}
"""
        
        try:
            result = self.structured_llm.invoke(prompt)
            return result.query[:3]  # Limit to 3 queries max
        except Exception as e:
            print(f"❌ Error generating quick queries: {e}")
            # Fallback queries
            return [
                f'"{plant_name}" owner company organization',
                f'"{plant_name}" location country address',
                f'"{plant_name}" parent company other power plants'
            ]
    
    def extract_org_info_from_results(self, plant_name: str, search_results: List[Dict]) -> Dict:
        """
        Extract organization information from search results
        
        Args:
            plant_name: Original plant name
            search_results: Web search results
            
        Returns:
            Dictionary with organization information
        """
        # Combine all search results into context
        context = ""
        for result in search_results:
            context += f"Title: {result.get('title', '')}\n"
            context += f"Content: {result.get('content', '')}\n"
            context += f"URL: {result.get('url', '')}\n\n"
        
        if not context.strip():
            return {
                "org_name": "Unknown Organization",
                "country": "Unknown",
                "plants": [{"name": plant_name, "status": "operational"}]
            }
        
        extraction_prompt = f"""Based on the search results below, extract the following information about the power plant "{plant_name}":

REQUIRED INFORMATION:
1. Organization Name: The FULL LEGAL NAME of the company/entity that owns/operates this power plant
   - Use the exact official company name as it appears in corporate documents
   - Include "Limited", "Ltd", "Corporation", "Corp", etc. if part of the legal name
   - For example: "Jindal Power Limited" not "Jindal Power" or "Jindal"
2. Country: The country where this power plant is located
3. Plant List: Other power plants owned by the SAME organization (not subsidiaries)

EXTRACTION RULES:
- Be precise and factual - use EXACT legal company names
- If information is unclear, use "Unknown" for that field
- For plant list, focus on major power plants owned by the same parent organization
- Include plant status if mentioned (operational, under_construction, decommissioned)
- CRITICAL: Extract the complete legal organization name, not abbreviated versions

FORMAT: Return a JSON object with exactly these keys:
{{
    "org_name": "Organization Name",
    "country": "Country Name", 
    "plants": [
        {{"name": "Plant Name 1", "status": "operational"}},
        {{"name": "Plant Name 2", "status": "operational"}}
    ]
}}

SEARCH RESULTS:
{context[:8000]}  # Limit context to avoid token limits

Plant Name: {plant_name}
"""
        
        try:
            # Use regular LLM for extraction (not structured output for flexibility)
            response = self.llm.invoke(extraction_prompt)
            
            # Try to parse JSON from response
            import json
            import re
            
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response.content, re.DOTALL)
            if json_match:
                org_info = json.loads(json_match.group())
                
                # Validate required fields
                if not org_info.get("org_name") or org_info["org_name"] == "Unknown Organization":
                    org_info["org_name"] = f"Unknown Organization ({plant_name})"
                
                if not org_info.get("country") or org_info["country"] == "Unknown":
                    org_info["country"] = "Unknown"
                
                if not org_info.get("plants"):
                    org_info["plants"] = [{"name": plant_name, "status": "operational"}]
                
                return org_info
            else:
                raise ValueError("No JSON found in response")
                
        except Exception as e:
            print(f"❌ Error extracting org info: {e}")
            # Fallback response
            return {
                "org_name": f"Unknown Organization ({plant_name})",
                "country": "Unknown",
                "plants": [{"name": plant_name, "status": "operational"}]
            }
    
    def discover_organization(self, plant_name: str, web_search_function) -> Dict:
        """
        Main method to discover organization information quickly
        
        Args:
            plant_name: Name of the power plant
            web_search_function: Function to perform web searches
            
        Returns:
            Dictionary with organization information
        """
        print(f"🔍 Starting quick organization discovery for: {plant_name}")
        
        # Step 1: Generate targeted queries
        queries = self.generate_quick_queries(plant_name)
        print(f"📝 Generated {len(queries)} targeted queries")
        
        # Step 2: Execute searches
        all_results = []
        for i, query in enumerate(queries, 1):
            print(f"🌐 Executing query {i}/{len(queries)}: {query[:50]}...")
            try:
                results = web_search_function(query)
                if results:
                    all_results.extend(results[:3])  # Limit results per query
            except Exception as e:
                print(f"⚠️ Search failed for query {i}: {e}")
                continue
        
        print(f"📊 Collected {len(all_results)} search results")
        
        # Step 3: Extract organization information
        org_info = self.extract_org_info_from_results(plant_name, all_results)
        
        print(f"✅ Quick discovery complete:")
        print(f"   Organization: {org_info['org_name']}")
        print(f"   Country: {org_info['country']}")
        print(f"   Plants found: {len(org_info['plants'])}")
        
        return org_info

# Global instance
quick_discovery = QuickOrgDiscovery()

def perform_quick_org_discovery(plant_name: str, web_search_function) -> Dict:
    """
    Convenience function for quick organization discovery
    
    Args:
        plant_name: Name of the power plant
        web_search_function: Function to perform web searches
        
    Returns:
        Dictionary with organization information
    """
    return quick_discovery.discover_organization(plant_name, web_search_function)