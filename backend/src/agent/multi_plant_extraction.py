"""
Multi-Plant Extraction Module

This module handles the extraction of 3-level data for all plants owned by an organization.
It implements Task 3: Running 3-level extraction for all plants discovered during 
Quick Organization Discovery.
"""

import time
import json
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

from agent.database_manager import get_database_manager
from agent.state import OverallState
from agent.utils import get_research_topic

class MultiPlantExtractor:
    """Handles multi-plant extraction orchestration"""
    
    def __init__(self):
        """Initialize multi-plant extractor"""
        self.db_manager = get_database_manager()
        self.extraction_progress = {}  # Track progress per session
    
    def get_organization_plants(self, org_uid: str, session_id: str = "unknown") -> List[Dict[str, Any]]:
        """
        Get all operational plants for the organization
        
        Args:
            org_uid: Organization unique ID
            session_id: Session ID for logging
            
        Returns:
            List of plant information dictionaries
        """
        try:
            print(f"[Session {session_id}] 🔍 Getting plants for organization UID: {org_uid}")
            
            plants = self.db_manager.get_plants_by_org_uid(org_uid, operational_only=True)
            
            print(f"[Session {session_id}] 📊 Found {len(plants)} operational plants:")
            for i, plant in enumerate(plants, 1):
                print(f"[Session {session_id}]   {i}. {plant['plant_name']} ({plant['country']})")
            
            return plants
            
        except Exception as e:
            print(f"[Session {session_id}] ❌ Error getting organization plants: {str(e)}")
            return []
    
    def generate_plant_session_id(self, main_session_id: str, plant_name: str) -> str:
        """
        Generate a plant-specific session ID
        
        Args:
            main_session_id: Main organization session ID
            plant_name: Name of the plant
            
        Returns:
            Plant-specific session ID
        """
        # Clean plant name for session ID
        clean_plant_name = plant_name.replace(" ", "_").replace("-", "_")
        clean_plant_name = "".join(c for c in clean_plant_name if c.isalnum() or c == "_")
        
        return f"{main_session_id}_{clean_plant_name}"
    
    def create_plant_state(self, plant_info: Dict[str, Any], main_state: OverallState, plant_session_id: str) -> OverallState:
        """
        Create a new state for individual plant processing
        
        Args:
            plant_info: Plant information from database
            main_state: Main organization state
            plant_session_id: Plant-specific session ID
            
        Returns:
            New state for plant processing
        """
        # Create a new state based on the main state but with plant-specific information
        plant_state = {
            **main_state,
            "session_id": plant_session_id,
            "plant_name": plant_info["plant_name"],
            "country": plant_info["country"],
            "org_uid": plant_info["org_uid"],
            "org_name": plant_info["org_name"],
            "plant_status": plant_info["plant_status"],
            "multi_plant_mode": True,
            "main_session_id": main_state.get("session_id", "unknown"),
            "plant_index": None,  # Will be set by caller
            "total_plants": None,  # Will be set by caller
            
            # Reset plant-specific data
            "plant_data": None,
            "unit_results": [],
            "extracted_units": [],
            "plant_level_complete": False,
            "unit_level_complete": False,
            
            # Keep organization-level data
            "org_data": main_state.get("org_data"),
            "org_level_complete": main_state.get("org_level_complete", False),
            
            # Reset processing flags
            "ready_for_unit_processing": False,
            "no_units_found": False,
            
            # Keep configuration
            "configuration": main_state.get("configuration"),
            "messages": main_state.get("messages", [])
        }
        
        return plant_state
    
    def update_extraction_progress(self, session_id: str, plant_name: str, status: str, details: Optional[Dict] = None):
        """
        Update extraction progress tracking
        
        Args:
            session_id: Main session ID
            plant_name: Name of the plant
            status: Current status (started, completed, failed)
            details: Optional additional details
        """
        if session_id not in self.extraction_progress:
            self.extraction_progress[session_id] = {}
        
        self.extraction_progress[session_id][plant_name] = {
            "status": status,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "details": details or {}
        }
    
    def get_extraction_summary(self, session_id: str) -> Dict[str, Any]:
        """
        Get extraction progress summary
        
        Args:
            session_id: Main session ID
            
        Returns:
            Progress summary dictionary
        """
        if session_id not in self.extraction_progress:
            return {"total_plants": 0, "completed": 0, "failed": 0, "in_progress": 0}
        
        progress = self.extraction_progress[session_id]
        
        total = len(progress)
        completed = sum(1 for p in progress.values() if p["status"] == "completed")
        failed = sum(1 for p in progress.values() if p["status"] == "failed")
        in_progress = sum(1 for p in progress.values() if p["status"] == "started")
        
        return {
            "total_plants": total,
            "completed": completed,
            "failed": failed,
            "in_progress": in_progress,
            "plants": progress
        }

def run_multi_plant_extraction(state: OverallState) -> OverallState:
    """
    LangGraph node for running multi-plant extraction
    
    This function orchestrates the extraction of 3-level data for all plants
    owned by the organization discovered in Quick Organization Discovery.
    
    Args:
        state: Current graph state
        
    Returns:
        Updated state with multi-plant extraction results
    """
    session_id = state.get("session_id", "unknown")
    org_uid = state.get("org_uid")
    
    print(f"[Session {session_id}] 🚀 ===== MULTI-PLANT EXTRACTION START =====")
    
    if not org_uid:
        error_msg = "No organization UID found for multi-plant extraction"
        print(f"[Session {session_id}] ❌ {error_msg}")
        return {
            **state,
            "multi_plant_extraction_complete": False,
            "multi_plant_error": error_msg,
            "plants_processed": 0
        }
    
    try:
        # Initialize extractor
        extractor = MultiPlantExtractor()
        
        # Get all plants for this organization
        plants = extractor.get_organization_plants(org_uid, session_id)
        
        if not plants:
            print(f"[Session {session_id}] ⚠️ No operational plants found for organization")
            return {
                **state,
                "multi_plant_extraction_complete": True,
                "multi_plant_error": "",
                "plants_processed": 0,
                "extraction_summary": "No operational plants found"
            }
        
        print(f"[Session {session_id}] 📋 Starting extraction for {len(plants)} plants")
        print(f"[Session {session_id}] ⏱️ Rate limiting: 60 seconds between plants")
        
        # Process each plant sequentially with rate limiting
        processed_plants = []
        failed_plants = []
        
        for i, plant_info in enumerate(plants):
            plant_name = plant_info["plant_name"]
            
            print(f"[Session {session_id}] 🔄 Processing plant {i+1}/{len(plants)}: {plant_name}")
            
            try:
                # Update progress tracking
                extractor.update_extraction_progress(session_id, plant_name, "started")
                
                # Generate plant-specific session ID
                plant_session_id = extractor.generate_plant_session_id(session_id, plant_name)
                
                # Create plant-specific state
                plant_state = extractor.create_plant_state(plant_info, state, plant_session_id)
                plant_state["plant_index"] = i + 1
                plant_state["total_plants"] = len(plants)
                
                print(f"[Session {session_id}] 🎯 Plant session: {plant_session_id}")
                
                # Run actual 3-level extraction for this specific plant
                print(f"[Session {plant_session_id}] 🔬 Running 3-level extraction...")
                print(f"[Session {plant_session_id}]   - Organization level: Using existing data")
                print(f"[Session {plant_session_id}]   - Plant level: Extracting for {plant_name}")
                print(f"[Session {plant_session_id}]   - Unit level: Will extract units for {plant_name}")

                # For now, we'll simulate the extraction process
                # TODO: Connect to actual 3-level extraction pipeline
                # This would involve calling the existing graph nodes for this specific plant

                # Simulate extraction completion
                extraction_result = {
                    "plant_name": plant_name,
                    "plant_session_id": plant_session_id,
                    "org_uid": org_uid,
                    "extraction_status": "simulated_completed",  # Mark as simulated for now
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "note": "This is a simulation - actual 3-level extraction integration pending"
                }
                
                processed_plants.append(extraction_result)
                
                # Update progress tracking
                extractor.update_extraction_progress(
                    session_id, 
                    plant_name, 
                    "completed", 
                    {"extraction_result": extraction_result}
                )
                
                print(f"[Session {session_id}] ✅ Plant {i+1}/{len(plants)} completed: {plant_name}")
                
            except Exception as e:
                error_msg = f"Failed to process {plant_name}: {str(e)}"
                print(f"[Session {session_id}] ❌ {error_msg}")
                
                failed_plants.append({
                    "plant_name": plant_name,
                    "error": error_msg,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                })
                
                # Update progress tracking
                extractor.update_extraction_progress(
                    session_id, 
                    plant_name, 
                    "failed", 
                    {"error": error_msg}
                )
            
            # Rate limiting: Wait 60 seconds between plants (except after the last plant)
            if i < len(plants) - 1:
                print(f"[Session {session_id}] ⏳ Waiting 60 seconds before next plant...")
                time.sleep(60)
        
        # Generate final summary
        summary = extractor.get_extraction_summary(session_id)
        
        print(f"[Session {session_id}] 🏁 Multi-plant extraction complete!")
        print(f"[Session {session_id}] 📊 Summary:")
        print(f"[Session {session_id}]   - Total plants: {summary['total_plants']}")
        print(f"[Session {session_id}]   - Completed: {summary['completed']}")
        print(f"[Session {session_id}]   - Failed: {summary['failed']}")
        
        return {
            **state,
            "multi_plant_extraction_complete": True,
            "multi_plant_error": "",
            "plants_processed": len(processed_plants),
            "processed_plants": processed_plants,
            "failed_plants": failed_plants,
            "extraction_summary": summary
        }
        
    except Exception as e:
        error_msg = f"Multi-plant extraction failed: {str(e)}"
        print(f"[Session {session_id}] ❌ {error_msg}")
        
        return {
            **state,
            "multi_plant_extraction_complete": False,
            "multi_plant_error": error_msg,
            "plants_processed": 0
        }

# Global multi-plant extractor instance
_multi_plant_extractor = None

def get_multi_plant_extractor() -> MultiPlantExtractor:
    """
    Get or create global multi-plant extractor instance
    
    Returns:
        MultiPlantExtractor instance
    """
    global _multi_plant_extractor
    if _multi_plant_extractor is None:
        _multi_plant_extractor = MultiPlantExtractor()
    return _multi_plant_extractor
